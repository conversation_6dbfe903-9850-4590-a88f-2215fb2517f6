import React, { useState } from 'react'
import { Plus, Trash2, Play, Calendar, Clock, BarChart3 } from 'lucide-react'
import { useGameStore, World } from '../store/gameStore'

interface WorldListProps {
  onWorldSelect: (worldId: string) => void
}

const WorldList: React.FC<WorldListProps> = ({ onWorldSelect }) => {
  const { worlds, addWorld, deleteWorld, loadWorld } = useGameStore()
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newWorldName, setNewWorldName] = useState('')
  const [newWorldDescription, setNewWorldDescription] = useState('')

  const handleCreateWorld = () => {
    if (newWorldName.trim()) {
      addWorld({
        name: newWorldName.trim(),
        description: newWorldDescription.trim() || undefined,
        progress: 0
      })
      setNewWorldName('')
      setNewWorldDescription('')
      setShowCreateForm(false)
    }
  }

  const handleDeleteWorld = (worldId: string, worldName: string) => {
    if (confirm(`Вы уверены, что хотите удалить мир "${worldName}"? Это действие нельзя отменить.`)) {
      deleteWorld(worldId)
    }
  }

  const handleLoadWorld = (worldId: string) => {
    loadWorld(worldId)
    onWorldSelect(worldId)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Create New World Button */}
      <div className="flex justify-center">
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
        >
          <Plus className="w-5 h-5" />
          Создать новый мир
        </button>
      </div>

      {/* Create World Form */}
      {showCreateForm && (
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-600">
          <h3 className="text-xl font-bold text-white mb-4">Создание нового мира</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Название мира *
              </label>
              <input
                type="text"
                value={newWorldName}
                onChange={(e) => setNewWorldName(e.target.value)}
                placeholder="Введите название мира..."
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={50}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Описание (необязательно)
              </label>
              <textarea
                value={newWorldDescription}
                onChange={(e) => setNewWorldDescription(e.target.value)}
                placeholder="Краткое описание мира..."
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={3}
                maxLength={200}
              />
            </div>
            <div className="flex gap-3">
              <button
                onClick={handleCreateWorld}
                disabled={!newWorldName.trim()}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
              >
                Создать
              </button>
              <button
                onClick={() => {
                  setShowCreateForm(false)
                  setNewWorldName('')
                  setNewWorldDescription('')
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Worlds List */}
      {worlds.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🌍</div>
          <h3 className="text-2xl font-bold text-white mb-2">Нет созданных миров</h3>
          <p className="text-gray-400">
            Создайте свой первый мир, чтобы начать приключение в постапокалиптической пустоши
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {worlds
            .sort((a, b) => new Date(b.lastPlayed).getTime() - new Date(a.lastPlayed).getTime())
            .map((world) => (
              <div
                key={world.id}
                className="bg-gray-800/70 backdrop-blur-sm rounded-lg p-6 border border-gray-600 hover:border-gray-500 transition-all duration-300"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-white mb-2">{world.name}</h3>
                    {world.description && (
                      <p className="text-gray-300 mb-3">{world.description}</p>
                    )}
                    
                    <div className="flex items-center gap-6 text-sm text-gray-400">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>Создан: {formatDate(world.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>Последняя игра: {formatDate(world.lastPlayed)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <BarChart3 className="w-4 h-4" />
                        <span>Прогресс: {world.progress}%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <button
                      onClick={() => handleLoadWorld(world.id)}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
                    >
                      <Play className="w-4 h-4" />
                      Играть
                    </button>
                    <button
                      onClick={() => handleDeleteWorld(world.id, world.name)}
                      className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                      title="Удалить мир"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}

export default WorldList
