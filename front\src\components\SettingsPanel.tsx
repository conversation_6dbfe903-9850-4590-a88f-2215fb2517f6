import React from 'react'
import { Volume2, VolumeX, Music, Speaker, Save, RotateCcw } from 'lucide-react'
import { useGameStore } from '../store/gameStore'

const SettingsPanel: React.FC = () => {
  const { settings, updateSettings } = useGameStore()

  const handleVolumeChange = (value: number) => {
    updateSettings({ volume: value / 100 })
  }

  const handleSoundToggle = () => {
    updateSettings({ soundEnabled: !settings.soundEnabled })
  }

  const handleMusicToggle = () => {
    updateSettings({ musicEnabled: !settings.musicEnabled })
  }

  const handleDifficultyChange = (difficulty: 'easy' | 'normal' | 'hard') => {
    updateSettings({ difficulty })
  }

  const handleAutoSaveToggle = () => {
    updateSettings({ autoSave: !settings.autoSave })
  }

  const resetToDefaults = () => {
    if (confirm('Сбросить все настройки к значениям по умолчанию?')) {
      updateSettings({
        volume: 0.7,
        soundEnabled: true,
        musicEnabled: true,
        difficulty: 'normal',
        autoSave: true
      })
    }
  }

  return (
    <div className="space-y-8">
      {/* Audio Settings */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-white border-b border-gray-600 pb-2">
          🔊 Аудио настройки
        </h3>
        
        {/* Master Volume */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-white font-medium">Общая громкость</label>
            <span className="text-gray-300">{Math.round(settings.volume * 100)}%</span>
          </div>
          <div className="flex items-center gap-4">
            <VolumeX className="w-5 h-5 text-gray-400" />
            <input
              type="range"
              min="0"
              max="100"
              value={Math.round(settings.volume * 100)}
              onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
              className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <Volume2 className="w-5 h-5 text-gray-400" />
          </div>
        </div>

        {/* Sound Effects Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Speaker className="w-5 h-5 text-gray-400" />
            <label className="text-white font-medium">Звуковые эффекты</label>
          </div>
          <button
            onClick={handleSoundToggle}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.soundEnabled ? 'bg-green-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.soundEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Music Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Music className="w-5 h-5 text-gray-400" />
            <label className="text-white font-medium">Фоновая музыка</label>
          </div>
          <button
            onClick={handleMusicToggle}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.musicEnabled ? 'bg-green-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.musicEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Game Settings */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-white border-b border-gray-600 pb-2">
          🎮 Игровые настройки
        </h3>

        {/* Difficulty */}
        <div className="space-y-3">
          <label className="text-white font-medium">Сложность</label>
          <div className="grid grid-cols-3 gap-2">
            {(['easy', 'normal', 'hard'] as const).map((diff) => (
              <button
                key={diff}
                onClick={() => handleDifficultyChange(diff)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  settings.difficulty === diff
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {diff === 'easy' && 'Легко'}
                {diff === 'normal' && 'Нормально'}
                {diff === 'hard' && 'Сложно'}
              </button>
            ))}
          </div>
          <p className="text-sm text-gray-400">
            {settings.difficulty === 'easy' && 'Больше ресурсов, меньше опасностей'}
            {settings.difficulty === 'normal' && 'Сбалансированный игровой процесс'}
            {settings.difficulty === 'hard' && 'Ограниченные ресурсы, высокие риски'}
          </p>
        </div>

        {/* Auto Save */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Save className="w-5 h-5 text-gray-400" />
            <div>
              <label className="text-white font-medium">Автосохранение</label>
              <p className="text-sm text-gray-400">Автоматически сохранять прогресс</p>
            </div>
          </div>
          <button
            onClick={handleAutoSaveToggle}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.autoSave ? 'bg-green-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.autoSave ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Reset Button */}
      <div className="pt-6 border-t border-gray-600">
        <button
          onClick={resetToDefaults}
          className="flex items-center gap-3 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          Сбросить к настройкам по умолчанию
        </button>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  )
}

export default SettingsPanel
