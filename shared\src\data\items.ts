import { InventoryItem } from '../models/InventoryItem';
import { ItemType, ItemRarity } from '../enums';

export const CONSUMABLES: InventoryItem[] = [
  {
    id: 'stimpak',
    name: 'Стимулятор',
    description: 'Медицинский стимулятор довоенного производства. Мгновенно восстанавливает здоровье.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 75,
    stackable: true,
    maxStack: 10,
    effects: [
      {
        type: 'heal',
        value: 50,
        duration: 0,
        description: 'Восстанавливает 50 ОЗ'
      }
    ],
    consumeOnUse: true,
    usageTime: 3,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чудо довоенной медицины. Каждый житель пустоши мечтает найти стимулятор.',
    createdAt: new Date(),
  },

  {
    id: 'rad-away',
    name: 'Антирадин',
    description: 'Антирадиационный препарат. Выводит радиацию из организма.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.1,
    value: 120,
    stackable: true,
    maxStack: 10,
    effects: [
      {
        type: 'remove_radiation',
        value: 100,
        duration: 0,
        description: 'Убирает 100 рад'
      }
    ],
    consumeOnUse: true,
    usageTime: 5,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Жизненно важное лекарство в мире, отравленном радиацией.',
    createdAt: new Date(),
  },

  {
    id: 'nuka-cola',
    name: 'Ядер-Кола',
    description: 'Довоенный газированный напиток. Содержит кофеин и радиоактивные изотопы.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 20,
    stackable: true,
    maxStack: 20,
    effects: [
      {
        type: 'heal',
        value: 10,
        duration: 0,
        description: 'Восстанавливает 10 ОЗ'
      },
      {
        type: 'action_points',
        value: 20,
        duration: 300,
        description: '+20 ОД на 5 минут'
      },
      {
        type: 'radiation',
        value: 3,
        duration: 0,
        description: '+3 рад'
      }
    ],
    consumeOnUse: true,
    usageTime: 2,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Культовый напиток довоенной Америки. Теперь слегка радиоактивный.',
    createdAt: new Date(),
  },

  {
    id: 'purified-water',
    name: 'Очищенная вода',
    description: 'Чистая питьевая вода. Бесценный ресурс в пустоши.',
    type: ItemType.CONSUMABLE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 15,
    stackable: true,
    maxStack: 20,
    effects: [
      {
        type: 'heal',
        value: 15,
        duration: 0,
        description: 'Восстанавливает 15 ОЗ'
      },
      {
        type: 'thirst',
        value: -50,
        duration: 0,
        description: 'Утоляет жажду'
      }
    ],
    consumeOnUse: true,
    usageTime: 3,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чистая вода - роскошь в мире после ядерной войны.',
    createdAt: new Date(),
  }
];

export const TOOLS: InventoryItem[] = [
  {
    id: 'lockpick',
    name: 'Отмычка',
    description: 'Простая металлическая отмычка для вскрытия замков.',
    type: ItemType.TOOL,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 5,
    stackable: true,
    maxStack: 50,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Незаменимый инструмент для любого исследователя пустоши.',
    createdAt: new Date(),
  },

  {
    id: 'crowbar',
    name: 'Лом',
    description: 'Тяжелый металлический лом. Может использоваться как оружие или инструмент.',
    type: ItemType.TOOL,
    rarity: ItemRarity.COMMON,
    weight: 3.0,
    value: 25,
    stackable: false,
    maxStack: 1,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: true,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Универсальный инструмент для вскрытия ящиков и самообороны.',
    createdAt: new Date(),
  },

  {
    id: 'geiger-counter',
    name: 'Счетчик Гейгера',
    description: 'Прибор для измерения уровня радиации. Издает характерные щелчки.',
    type: ItemType.TOOL,
    rarity: ItemRarity.UNCOMMON,
    weight: 1.0,
    value: 200,
    stackable: false,
    maxStack: 1,
    effects: [
      {
        type: 'radiation_detection',
        value: 1,
        duration: 0,
        description: 'Показывает уровень радиации'
      }
    ],
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Жизненно важный прибор для выживания в радиоактивной пустоши.',
    createdAt: new Date(),
  }
];

export const RESOURCES: InventoryItem[] = [
  {
    id: 'metal-parts',
    name: 'Металлические детали',
    description: 'Различные металлические части и компоненты. Полезны для ремонта и создания.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.5,
    value: 8,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
  
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Металлолом - основа экономики пустоши.',
    createdAt: new Date(),
  },

  {
    id: 'electronic_parts',
    name: 'Электронные компоненты',
    description: 'Микросхемы, провода и другие электронные детали.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.UNCOMMON,
    weight: 0.2,
    value: 15,
    stackable: true,
    maxStack: 50,
    effects: [],
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Сложная электроника - редкость в постапокалиптическом мире.',
    createdAt: new Date(),
  },

  {
    id: 'cloth',
    name: 'Ткань',
    description: 'Куски ткани различного качества. Используется для пошива и ремонта.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.COMMON,
    weight: 0.1,
    value: 2,
    stackable: true,
    maxStack: 100,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Простая ткань, но в пустоши ценится каждый лоскут.',
    createdAt: new Date(),
  },

  {
    id: 'fusion_core',
    name: 'Ядерный блок',
    description: 'Портативный ядерный реактор. Источник энергии для силовой брони.',
    type: ItemType.RESOURCE,
    rarity: ItemRarity.RARE,
    weight: 3.0,
    value: 500,
    stackable: true,
    maxStack: 10,
    consumeOnUse: false,
    usageTime: 0,
    canBeCrafted: false,
    canBeSold: true,
    canBeDropped: true,
    isQuestItem: false,
    loreText: 'Чудо довоенной технологии. Содержит энергию небольшой звезды.',
    createdAt: new Date(),
  }
];

// Объединенный экспорт всех предметов
export const ALL_ITEMS: InventoryItem[] = [
  ...CONSUMABLES,
  ...TOOLS,
  ...RESOURCES
];
