import React, { useState } from 'react'
import { Play, Settings, X } from 'lucide-react'
import WorldList from './WorldList'
import SettingsPanel from './SettingsPanel'

interface MainMenuProps {
  onStartGame?: (worldId: string) => void
}

const MainMenu: React.FC<MainMenuProps> = ({ onStartGame }) => {
  const [activePanel, setActivePanel] = useState<'main' | 'worlds' | 'settings'>('main')

  const handlePlayClick = () => {
    setActivePanel('worlds')
  }

  const handleSettingsClick = () => {
    setActivePanel('settings')
  }

  const handleBackToMain = () => {
    setActivePanel('main')
  }

  const handleWorldSelect = (worldId: string) => {
    onStartGame?.(worldId)
  }

  return (
    <div 
      className="min-h-screen bg-cover bg-center bg-no-repeat relative flex items-center justify-center"
      style={{
        backgroundImage: 'url(/MainManuBG.jpg)',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black/50" />
      
      {/* Main content */}
      <div className="relative z-10 w-full max-w-4xl mx-auto px-4">
        
        {/* Main Menu */}
        {activePanel === 'main' && (
          <div className="text-center">
            {/* Game Title */}
            <div className="mb-12">
              <h1 className="text-7xl font-bold mb-4 text-white drop-shadow-2xl">
                ☢️ NuclearStory
              </h1>
              <p className="text-xl text-gray-200 drop-shadow-lg max-w-2xl mx-auto">
                Выживите в постапокалиптической пустоши в этой интерактивной истории, управляемой ИИ
              </p>
            </div>

            {/* Menu Buttons */}
            <div className="space-y-6">
              <button
                onClick={handlePlayClick}
                className="group flex items-center justify-center gap-4 w-full max-w-md mx-auto px-8 py-6 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white text-2xl font-bold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-2xl"
              >
                <Play className="w-8 h-8 group-hover:scale-110 transition-transform" />
                Играть
              </button>

              <button
                onClick={handleSettingsClick}
                className="group flex items-center justify-center gap-4 w-full max-w-md mx-auto px-8 py-6 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white text-2xl font-bold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-2xl"
              >
                <Settings className="w-8 h-8 group-hover:rotate-90 transition-transform duration-300" />
                Настройки
              </button>
            </div>
          </div>
        )}

        {/* World Selection Panel */}
        {activePanel === 'worlds' && (
          <div className="bg-black/80 backdrop-blur-sm rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-3xl font-bold text-white">Выбор мира</h2>
              <button
                onClick={handleBackToMain}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-8 h-8" />
              </button>
            </div>
            <WorldList onWorldSelect={handleWorldSelect} />
          </div>
        )}

        {/* Settings Panel */}
        {activePanel === 'settings' && (
          <div className="bg-black/80 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-3xl font-bold text-white">Настройки</h2>
              <button
                onClick={handleBackToMain}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-8 h-8" />
              </button>
            </div>
            <SettingsPanel />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainMenu
