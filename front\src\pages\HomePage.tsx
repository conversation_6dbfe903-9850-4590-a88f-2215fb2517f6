import { Link } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'
import styles from './HomePage.module.css'

const HomePage = () => {
  const { isAuthenticated } = useAuthStore()

  return (
    <div className={styles.homePage}>
      <div className={styles.container}>
        <h1 className={styles.title}>
          ☢️ NuclearStory
        </h1>
        <p className={styles.subtitle}>
          Survive the post-apocalyptic wasteland in this AI-driven interactive story game.
          Make critical decisions, manage your resources, and uncover the mysteries of the nuclear aftermath.
        </p>

        <div className={styles.featuresGrid}>
          <div className={styles.featureCard}>
            <h3 className={styles.featureTitle}>🎯 Dynamic Quests</h3>
            <p className={styles.featureDescription}>
              Experience procedurally generated quests and events that adapt to your choices and survival style.
            </p>
          </div>

          <div className={styles.featureCard}>
            <h3 className={styles.featureTitle}>🤖 AI Storytelling</h3>
            <p className={styles.featureDescription}>
              Immerse yourself in AI-generated narratives that respond to your decisions and create unique storylines.
            </p>
          </div>

          <div className={styles.featureCard}>
            <h3 className={styles.featureTitle}>⚡ Survival Mechanics</h3>
            <p className={styles.featureDescription}>
              Manage hunger, thirst, radiation exposure, and health while exploring the dangerous wasteland.
            </p>
          </div>
        </div>

        <div className={styles.buttonGroup}>
          <Link
            to="/menu"
            className={`${styles.button} ${styles.primaryButton}`}
          >
            🎮 Главное меню
          </Link>

          {isAuthenticated ? (
            <Link
              to="/signup"
              className={`${styles.button} ${styles.secondaryButton}`}
            >
              🎮 Start Your Survival
            </Link>
          ) : (
            <>
              <Link
                to="/signup"
                className={`${styles.button} ${styles.secondaryButton}`}
              >
                🎮 Start Your Survival
              </Link>
              <Link
                to="/login"
                className={`${styles.button} ${styles.tertiaryButton}`}
              >
                Continue Existing Game
              </Link>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default HomePage
