import { Link } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'

const HomePage = () => {
  const { isAuthenticated } = useAuthStore()

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="text-center max-w-4xl mx-auto">
        <h1 className="text-6xl font-bold mb-6 text-primary">
          ☢️ NuclearStory
        </h1>
        <p className="text-xl text-muted-foreground mb-8">
          Survive the post-apocalyptic wasteland in this AI-driven interactive story game.
          Make critical decisions, manage your resources, and uncover the mysteries of the nuclear aftermath.
        </p>
        
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="bg-card p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-3 text-primary">🎯 Dynamic Quests</h3>
            <p className="text-muted-foreground">
              Experience procedurally generated quests and events that adapt to your choices and survival style.
            </p>
          </div>
          
          <div className="bg-card p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-3 text-primary">🤖 AI Storytelling</h3>
            <p className="text-muted-foreground">
              Immerse yourself in AI-generated narratives that respond to your decisions and create unique storylines.
            </p>
          </div>
          
          <div className="bg-card p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-3 text-primary">⚡ Survival Mechanics</h3>
            <p className="text-muted-foreground">
              Manage hunger, thirst, radiation exposure, and health while exploring the dangerous wasteland.
            </p>
          </div>
        </div>

        <div className="space-x-4">
          
          {isAuthenticated ? (
          <Link
            to="/menu"
            className="inline-block px-8 py-4 bg-primary text-primary-foreground text-lg font-semibold rounded-lg hover:bg-primary/90 transition-colors"
          >
            🎮 Главное меню
          </Link>
          ) : (
            <>
              <Link
                to="/signup"
                className="inline-block px-8 py-4 bg-secondary text-secondary-foreground text-lg font-semibold rounded-lg hover:bg-secondary/90 transition-colors"
              >
              🎮  Start Your Survival
              </Link>
            
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default HomePage
